@echo off
title Minecraft 4K 120fps Recorder
echo ========================================
echo   Minecraft 4K 120fps Recorder
echo ========================================
echo.

REM Проверяем наличие Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ОШИБКА: Python не найден!
    echo Установите Python 3.9+ с https://python.org
    echo.
    pause
    exit /b 1
)

REM Проверяем наличие main.py
if not exist "main.py" (
    echo ОШИБКА: Файл main.py не найден!
    echo Убедитесь, что вы запускаете из правильной папки.
    echo.
    pause
    exit /b 1
)

REM Проверяем установку зависимостей
echo Проверяем зависимости...
pip show customtkinter >nul 2>&1
if errorlevel 1 (
    echo Устанавливаем зависимости...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ОШИБКА: Не удалось установить зависимости!
        echo Попробуйте выполнить: pip install -r requirements.txt
        echo.
        pause
        exit /b 1
    )
)

echo Запускаем программу...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo Программа завершилась с ошибкой.
    echo Проверьте консоль выше для деталей.
    echo.
)

pause
