# 🚀 Быстрый запуск Minecraft 4K 120fps Recorder

## ✅ Зависимости установлены!

Все необходимые Python-пакеты уже установлены:
- ✅ CustomTkinter (GUI)
- ✅ OpenCV (видео обработка)
- ✅ NumPy (математические операции)
- ✅ PSUtil (мониторинг системы)
- ✅ Keyboard (горячие клавиши)
- ✅ MSS (захват экрана)
- ✅ PyAudio (аудио)
- ✅ PyWin32 (Windows интеграция)

## 🎯 Следующие шаги:

### 1. Скачайте FFmpeg
**Это обязательно для работы программы!**

**Быстрый способ:**
1. Перейдите на https://www.gyan.dev/ffmpeg/builds/
2. Скачайте "release builds" → "ffmpeg-release-essentials.zip"
3. Распакуйте в папку `C:\ffmpeg`
4. Путь к файлу: `C:\ffmpeg\bin\ffmpeg.exe`

**Подробные инструкции:** см. файл `FFMPEG_SETUP.md`

### 2. Запустите программу
```bash
# Способ 1: Через Python
python main.py

# Способ 2: Через bat-файл (рекомендуется)
start_recorder.bat
```

### 3. Настройте программу
1. **Укажите путь к FFmpeg** (например: `C:\ffmpeg\bin\ffmpeg.exe`)
2. **Нажмите "Проверить FFmpeg"** для тестирования
3. **Выберите профиль качества:**
   - **Максимальное качество** - 4K 120fps (требует мощный ПК)
   - **Высокое качество** - 1440p 60fps
   - **Для стриминга** - 1080p 60fps
   - **Архивное** - 1080p 30fps

4. **Включите "Запись в полноэкранном режиме"** если нужно записывать весь экран
5. **Выберите папку для сохранения** записей

### 4. Начните запись
- **Кнопка "Начать запись"** или нажмите **F9**
- **F10** - пауза/продолжить
- **F11** - скриншот
- **F12** - настройки

## ⚡ Горячие клавиши (работают глобально):
- **F9** - Начать/остановить запись
- **F10** - Пауза/продолжить запись
- **F11** - Сделать скриншот
- **F12** - Открыть настройки

## 🎮 Рекомендации для Minecraft:

### Настройки Minecraft:
1. **Используйте Optifine или Sodium** для оптимизации
2. **Ограничьте FPS до 120** в настройках игры
3. **Отключите V-Sync** в Minecraft
4. **Играйте в полноэкранном режиме**

### Настройки Windows:
1. **Включите игровой режим** (Win + G → Настройки → Игровой режим)
2. **Высокая производительность** в настройках питания
3. **Закройте ненужные программы** перед записью

### Для максимального качества 4K 120fps:
- **Мощная видеокарта:** RTX 4070+ или RX 7800 XT+
- **Процессор:** i7-12700K+ или Ryzen 7 5800X+
- **Память:** 32 GB RAM
- **Диск:** SSD NVMe для записи

## 🔧 Если что-то не работает:

### FFmpeg не найден:
1. Проверьте правильность пути к `ffmpeg.exe`
2. Убедитесь, что файл существует
3. Попробуйте переустановить FFmpeg

### Низкая производительность:
1. Выберите профиль с меньшим разрешением
2. Снизьте FPS в настройках профиля
3. Включите аппаратное ускорение
4. Закройте другие программы

### Программа не запускается:
1. Убедитесь, что Python 3.9+ установлен
2. Проверьте, что все зависимости установлены
3. Запустите через `start_recorder.bat`

## 📁 Структура файлов:
```
📁 Minecraft 4K Recorder/
├── 📄 main.py              # Основная программа
├── 📄 requirements.txt     # Зависимости (установлены)
├── 📄 start_recorder.bat   # Быстрый запуск
├── 📄 README.md           # Полная документация
├── 📄 FFMPEG_SETUP.md     # Установка FFmpeg
└── 📄 QUICK_START.md      # Этот файл
```

## 🎬 Готово к записи!

После настройки FFmpeg программа готова записывать Minecraft в максимальном качестве 4K 120fps!

**Удачной записи! 🎮✨**
