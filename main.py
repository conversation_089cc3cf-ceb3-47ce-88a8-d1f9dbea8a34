#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minecraft 4K 120fps Recorder
Профессиональная программа для записи Minecraft с максимальным качеством
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
import threading
import subprocess
import time
import os
import json
import psutil
import cv2
import numpy as np
from datetime import datetime
import keyboard
import mss
from PIL import Image, ImageTk
import configparser

# Настройка темы CustomTkinter
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class MinecraftRecorder:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Minecraft 4K 120fps Recorder")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # Состояние записи
        self.is_recording = False
        self.is_paused = False
        self.ffmpeg_process = None
        self.start_time = None
        self.output_file = None
        
        # Настройки по умолчанию
        self.settings = {
            'output_dir': os.path.expanduser('~/Videos/Minecraft'),
            'quality_profile': 'maximum',
            'capture_area': 'fullscreen',
            'audio_enabled': True,
            'show_fps': True,
            'hardware_acceleration': True,
            'custom_resolution': '3840x2160',
            'custom_fps': 120,
            'custom_bitrate': 100000
        }
        
        # Профили качества
        self.quality_profiles = {
            'maximum': {
                'name': 'Максимальное качество (4K 120fps)',
                'resolution': '3840x2160',
                'fps': 120,
                'bitrate': 100000,
                'codec': 'libx265',
                'preset': 'medium',
                'crf': 18
            },
            'high': {
                'name': 'Высокое качество (1440p 60fps)',
                'resolution': '2560x1440',
                'fps': 60,
                'bitrate': 25000,
                'codec': 'libx264',
                'preset': 'fast',
                'crf': 20
            },
            'streaming': {
                'name': 'Для стриминга (1080p 60fps)',
                'resolution': '1920x1080',
                'fps': 60,
                'bitrate': 15000,
                'codec': 'libx264',
                'preset': 'veryfast',
                'crf': 22
            },
            'archive': {
                'name': 'Архивное (1080p 30fps)',
                'resolution': '1920x1080',
                'fps': 30,
                'bitrate': 8000,
                'codec': 'libx264',
                'preset': 'slow',
                'crf': 24
            }
        }
        
        self.load_settings()
        self.create_interface()
        self.setup_hotkeys()
        self.start_monitoring()
        
    def create_interface(self):
        """Создание графического интерфейса"""
        # Главное меню
        self.create_menu()
        
        # Основной фрейм
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Левая панель - настройки
        left_panel = ctk.CTkFrame(main_frame)
        left_panel.pack(side="left", fill="y", padx=(0, 10))
        
        # Правая панель - превью и мониторинг
        right_panel = ctk.CTkFrame(main_frame)
        right_panel.pack(side="right", fill="both", expand=True)
        
        self.create_settings_panel(left_panel)
        self.create_preview_panel(right_panel)
        self.create_control_panel(main_frame)
        
    def create_menu(self):
        """Создание меню приложения"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Файл
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Файл", menu=file_menu)
        file_menu.add_command(label="Открыть папку записей", command=self.open_output_folder)
        file_menu.add_separator()
        file_menu.add_command(label="Выход", command=self.root.quit)
        
        # Настройки
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Настройки", menu=settings_menu)
        settings_menu.add_command(label="Горячие клавиши", command=self.show_hotkeys)
        settings_menu.add_command(label="Дополнительно", command=self.show_advanced_settings)
        
        # Помощь
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Помощь", menu=help_menu)
        help_menu.add_command(label="О программе", command=self.show_about)
        
    def create_settings_panel(self, parent):
        """Панель настроек"""
        # Заголовок
        title = ctk.CTkLabel(parent, text="Настройки записи", font=ctk.CTkFont(size=18, weight="bold"))
        title.pack(pady=(10, 20))
        
        # Профиль качества
        profile_frame = ctk.CTkFrame(parent)
        profile_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(profile_frame, text="Профиль качества:").pack(anchor="w", padx=10, pady=(10, 5))
        
        self.profile_var = tk.StringVar(value=self.settings['quality_profile'])
        profile_menu = ctk.CTkOptionMenu(
            profile_frame,
            variable=self.profile_var,
            values=list(self.quality_profiles.keys()),
            command=self.on_profile_change
        )
        profile_menu.pack(fill="x", padx=10, pady=(0, 10))
        
        # Информация о профиле
        self.profile_info = ctk.CTkTextbox(profile_frame, height=100)
        self.profile_info.pack(fill="x", padx=10, pady=(0, 10))
        self.update_profile_info()
        
        # Область захвата
        capture_frame = ctk.CTkFrame(parent)
        capture_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(capture_frame, text="Область захвата:").pack(anchor="w", padx=10, pady=(10, 5))
        
        self.capture_var = tk.StringVar(value=self.settings['capture_area'])
        capture_options = ["fullscreen", "window", "custom"]
        capture_menu = ctk.CTkOptionMenu(capture_frame, variable=self.capture_var, values=capture_options)
        capture_menu.pack(fill="x", padx=10, pady=(0, 10))
        
        # Папка вывода
        output_frame = ctk.CTkFrame(parent)
        output_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(output_frame, text="Папка для записей:").pack(anchor="w", padx=10, pady=(10, 5))
        
        output_path_frame = ctk.CTkFrame(output_frame)
        output_path_frame.pack(fill="x", padx=10, pady=(0, 5))
        
        self.output_path_var = tk.StringVar(value=self.settings['output_dir'])
        output_entry = ctk.CTkEntry(output_path_frame, textvariable=self.output_path_var)
        output_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
        
        browse_btn = ctk.CTkButton(output_path_frame, text="Обзор", width=80, command=self.browse_output_folder)
        browse_btn.pack(side="right")
        
        # Дополнительные настройки
        extra_frame = ctk.CTkFrame(parent)
        extra_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(extra_frame, text="Дополнительно:").pack(anchor="w", padx=10, pady=(10, 5))
        
        self.audio_var = tk.BooleanVar(value=self.settings['audio_enabled'])
        audio_check = ctk.CTkCheckBox(extra_frame, text="Записывать звук", variable=self.audio_var)
        audio_check.pack(anchor="w", padx=10, pady=2)
        
        self.fps_var = tk.BooleanVar(value=self.settings['show_fps'])
        fps_check = ctk.CTkCheckBox(extra_frame, text="Показывать FPS", variable=self.fps_var)
        fps_check.pack(anchor="w", padx=10, pady=2)
        
        self.hw_accel_var = tk.BooleanVar(value=self.settings['hardware_acceleration'])
        hw_check = ctk.CTkCheckBox(extra_frame, text="Аппаратное ускорение", variable=self.hw_accel_var)
        hw_check.pack(anchor="w", padx=10, pady=(2, 10))
