#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minecraft 4K 120fps Recorder
Профессиональная программа для записи Minecraft с максимальным качеством
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
import threading
import subprocess
import time
import os
import json
import psutil
import cv2
import numpy as np
from datetime import datetime
import keyboard
import mss
from PIL import Image, ImageTk
import configparser

# Настройка темы CustomTkinter
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class MinecraftRecorder:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Minecraft 4K 120fps Recorder")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # Состояние записи
        self.is_recording = False
        self.is_paused = False
        self.ffmpeg_process = None
        self.start_time = None
        self.output_file = None
        
        # Настройки по умолчанию
        self.settings = {
            'output_dir': os.path.expanduser('~/Videos/Minecraft'),
            'ffmpeg_path': '',  # Путь до FFmpeg
            'quality_profile': 'maximum',
            'capture_area': 'fullscreen',
            'fullscreen_recording': True,  # Запись в полноэкранном режиме
            'audio_enabled': True,
            'show_fps': True,
            'hardware_acceleration': True,
            'custom_resolution': '3840x2160',
            'custom_fps': 120,
            'custom_bitrate': 100000
        }
        
        # Профили качества
        self.quality_profiles = {
            'maximum': {
                'name': 'Максимальное качество (4K 120fps)',
                'resolution': '3840x2160',
                'fps': 120,
                'bitrate': 100000,
                'codec': 'libx265',
                'preset': 'medium',
                'crf': 18
            },
            'high': {
                'name': 'Высокое качество (1440p 60fps)',
                'resolution': '2560x1440',
                'fps': 60,
                'bitrate': 25000,
                'codec': 'libx264',
                'preset': 'fast',
                'crf': 20
            },
            'streaming': {
                'name': 'Для стриминга (1080p 60fps)',
                'resolution': '1920x1080',
                'fps': 60,
                'bitrate': 15000,
                'codec': 'libx264',
                'preset': 'veryfast',
                'crf': 22
            },
            'archive': {
                'name': 'Архивное (1080p 30fps)',
                'resolution': '1920x1080',
                'fps': 30,
                'bitrate': 8000,
                'codec': 'libx264',
                'preset': 'slow',
                'crf': 24
            }
        }
        
        self.load_settings()
        self.create_interface()
        self.setup_hotkeys()
        self.start_monitoring()
        
    def create_interface(self):
        """Создание графического интерфейса"""
        # Главное меню
        self.create_menu()
        
        # Основной фрейм
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Левая панель - настройки
        left_panel = ctk.CTkFrame(main_frame)
        left_panel.pack(side="left", fill="y", padx=(0, 10))
        
        # Правая панель - превью и мониторинг
        right_panel = ctk.CTkFrame(main_frame)
        right_panel.pack(side="right", fill="both", expand=True)
        
        self.create_settings_panel(left_panel)
        self.create_preview_panel(right_panel)
        self.create_control_panel(main_frame)
        
    def create_menu(self):
        """Создание меню приложения"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Файл
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Файл", menu=file_menu)
        file_menu.add_command(label="Открыть папку записей", command=self.open_output_folder)
        file_menu.add_separator()
        file_menu.add_command(label="Выход", command=self.root.quit)
        
        # Настройки
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Настройки", menu=settings_menu)
        settings_menu.add_command(label="Горячие клавиши", command=self.show_hotkeys)
        settings_menu.add_command(label="Дополнительно", command=self.show_advanced_settings)
        
        # Помощь
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Помощь", menu=help_menu)
        help_menu.add_command(label="О программе", command=self.show_about)
        
    def create_settings_panel(self, parent):
        """Панель настроек"""
        # Заголовок
        title = ctk.CTkLabel(parent, text="Настройки записи", font=ctk.CTkFont(size=18, weight="bold"))
        title.pack(pady=(10, 20))
        
        # Профиль качества
        profile_frame = ctk.CTkFrame(parent)
        profile_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(profile_frame, text="Профиль качества:").pack(anchor="w", padx=10, pady=(10, 5))
        
        self.profile_var = tk.StringVar(value=self.settings['quality_profile'])
        profile_menu = ctk.CTkOptionMenu(
            profile_frame,
            variable=self.profile_var,
            values=list(self.quality_profiles.keys()),
            command=self.on_profile_change
        )
        profile_menu.pack(fill="x", padx=10, pady=(0, 10))
        
        # Информация о профиле
        self.profile_info = ctk.CTkTextbox(profile_frame, height=100)
        self.profile_info.pack(fill="x", padx=10, pady=(0, 10))
        self.update_profile_info()
        
        # Область захвата
        capture_frame = ctk.CTkFrame(parent)
        capture_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(capture_frame, text="Область захвата:").pack(anchor="w", padx=10, pady=(10, 5))
        
        self.capture_var = tk.StringVar(value=self.settings['capture_area'])
        capture_options = ["fullscreen", "window", "custom"]
        capture_menu = ctk.CTkOptionMenu(capture_frame, variable=self.capture_var, values=capture_options)
        capture_menu.pack(fill="x", padx=10, pady=(0, 10))
        
        # Путь к FFmpeg
        ffmpeg_frame = ctk.CTkFrame(parent)
        ffmpeg_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(ffmpeg_frame, text="Путь к FFmpeg:").pack(anchor="w", padx=10, pady=(10, 5))

        ffmpeg_path_frame = ctk.CTkFrame(ffmpeg_frame)
        ffmpeg_path_frame.pack(fill="x", padx=10, pady=(0, 5))

        self.ffmpeg_path_var = tk.StringVar(value=self.settings['ffmpeg_path'])
        ffmpeg_entry = ctk.CTkEntry(ffmpeg_path_frame, textvariable=self.ffmpeg_path_var,
                                   placeholder_text="Например: C:\\ffmpeg\\bin\\ffmpeg.exe")
        ffmpeg_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))

        browse_ffmpeg_btn = ctk.CTkButton(ffmpeg_path_frame, text="Обзор", width=80, command=self.browse_ffmpeg)
        browse_ffmpeg_btn.pack(side="right")

        # Кнопка проверки FFmpeg
        check_ffmpeg_btn = ctk.CTkButton(ffmpeg_frame, text="Проверить FFmpeg", command=self.check_ffmpeg)
        check_ffmpeg_btn.pack(pady=(0, 10), padx=10)

        # Папка вывода
        output_frame = ctk.CTkFrame(parent)
        output_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(output_frame, text="Папка для записей:").pack(anchor="w", padx=10, pady=(10, 5))

        output_path_frame = ctk.CTkFrame(output_frame)
        output_path_frame.pack(fill="x", padx=10, pady=(0, 5))

        self.output_path_var = tk.StringVar(value=self.settings['output_dir'])
        output_entry = ctk.CTkEntry(output_path_frame, textvariable=self.output_path_var)
        output_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))

        browse_btn = ctk.CTkButton(output_path_frame, text="Обзор", width=80, command=self.browse_output_folder)
        browse_btn.pack(side="right")
        
        # Дополнительные настройки
        extra_frame = ctk.CTkFrame(parent)
        extra_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(extra_frame, text="Дополнительно:").pack(anchor="w", padx=10, pady=(10, 5))
        
        self.audio_var = tk.BooleanVar(value=self.settings['audio_enabled'])
        audio_check = ctk.CTkCheckBox(extra_frame, text="Записывать звук", variable=self.audio_var)
        audio_check.pack(anchor="w", padx=10, pady=2)

        self.fullscreen_var = tk.BooleanVar(value=self.settings['fullscreen_recording'])
        fullscreen_check = ctk.CTkCheckBox(extra_frame, text="Запись в полноэкранном режиме", variable=self.fullscreen_var)
        fullscreen_check.pack(anchor="w", padx=10, pady=2)

        self.fps_var = tk.BooleanVar(value=self.settings['show_fps'])
        fps_check = ctk.CTkCheckBox(extra_frame, text="Показывать FPS", variable=self.fps_var)
        fps_check.pack(anchor="w", padx=10, pady=2)

        self.hw_accel_var = tk.BooleanVar(value=self.settings['hardware_acceleration'])
        hw_check = ctk.CTkCheckBox(extra_frame, text="Аппаратное ускорение", variable=self.hw_accel_var)
        hw_check.pack(anchor="w", padx=10, pady=(2, 10))

    def browse_ffmpeg(self):
        """Выбор пути к FFmpeg"""
        filename = filedialog.askopenfilename(
            title="Выберите FFmpeg",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if filename:
            self.ffmpeg_path_var.set(filename)
            self.settings['ffmpeg_path'] = filename
            self.save_settings()

    def check_ffmpeg(self):
        """Проверка работоспособности FFmpeg"""
        ffmpeg_path = self.ffmpeg_path_var.get().strip()

        if not ffmpeg_path:
            messagebox.showerror("Ошибка", "Укажите путь к FFmpeg")
            return

        if not os.path.exists(ffmpeg_path):
            messagebox.showerror("Ошибка", "Файл FFmpeg не найден")
            return

        try:
            # Проверяем версию FFmpeg
            result = subprocess.run([ffmpeg_path, "-version"],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                version_info = result.stdout.split('\n')[0]
                messagebox.showinfo("Успех", f"FFmpeg найден!\n{version_info}")
            else:
                messagebox.showerror("Ошибка", "Не удается запустить FFmpeg")

        except subprocess.TimeoutExpired:
            messagebox.showerror("Ошибка", "Таймаут при проверке FFmpeg")
        except Exception as e:
            messagebox.showerror("Ошибка", f"Ошибка при проверке FFmpeg: {str(e)}")

    def get_ffmpeg_command(self):
        """Генерация команды FFmpeg для записи"""
        ffmpeg_path = self.ffmpeg_path_var.get().strip()
        if not ffmpeg_path:
            raise ValueError("Не указан путь к FFmpeg")

        profile = self.quality_profiles[self.profile_var.get()]

        # Базовая команда
        cmd = [ffmpeg_path]

        # Входные параметры для захвата экрана
        if self.fullscreen_var.get():
            # Полноэкранный захват
            cmd.extend([
                "-f", "gdigrab",
                "-framerate", str(profile['fps']),
                "-i", "desktop"
            ])
        else:
            # Захват окна (можно расширить для выбора конкретного окна)
            cmd.extend([
                "-f", "gdigrab",
                "-framerate", str(profile['fps']),
                "-i", "title=Minecraft"
            ])

        # Аудио
        if self.audio_var.get():
            cmd.extend([
                "-f", "dshow",
                "-i", "audio=virtual-audio-capturer"
            ])

        # Видео кодек и настройки
        if self.hw_accel_var.get():
            # Аппаратное ускорение NVIDIA
            cmd.extend(["-c:v", "h264_nvenc"])
            if profile['codec'] == 'libx265':
                cmd.extend(["-c:v", "hevc_nvenc"])
        else:
            cmd.extend(["-c:v", profile['codec']])

        # Качество и битрейт
        cmd.extend([
            "-b:v", f"{profile['bitrate']}k",
            "-maxrate", f"{int(profile['bitrate'] * 1.2)}k",
            "-bufsize", f"{int(profile['bitrate'] * 2)}k",
            "-preset", profile['preset'],
            "-crf", str(profile['crf'])
        ])

        # Разрешение
        if profile['resolution'] != 'auto':
            cmd.extend(["-s", profile['resolution']])

        # Аудио кодек
        if self.audio_var.get():
            cmd.extend(["-c:a", "aac", "-b:a", "320k"])

        # Выходной файл
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(
            self.output_path_var.get(),
            f"minecraft_recording_{timestamp}.mp4"
        )

        cmd.extend([
            "-y",  # Перезаписать файл если существует
            output_file
        ])

        return cmd, output_file

    def create_preview_panel(self, parent):
        """Панель превью и мониторинга"""
        # Заголовок
        title = ctk.CTkLabel(parent, text="Превью и мониторинг", font=ctk.CTkFont(size=18, weight="bold"))
        title.pack(pady=(10, 20))

        # Превью экрана (заглушка)
        preview_frame = ctk.CTkFrame(parent)
        preview_frame.pack(fill="both", expand=True, padx=10, pady=5)

        self.preview_label = ctk.CTkLabel(preview_frame, text="Превью экрана\n(будет показано при записи)")
        self.preview_label.pack(expand=True)

        # Информация о производительности
        perf_frame = ctk.CTkFrame(parent)
        perf_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(perf_frame, text="Производительность:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))

        self.fps_label = ctk.CTkLabel(perf_frame, text="FPS: --")
        self.fps_label.pack(anchor="w", padx=10, pady=2)

        self.cpu_label = ctk.CTkLabel(perf_frame, text="CPU: --%")
        self.cpu_label.pack(anchor="w", padx=10, pady=2)

        self.memory_label = ctk.CTkLabel(perf_frame, text="RAM: -- MB")
        self.memory_label.pack(anchor="w", padx=10, pady=2)

        self.disk_label = ctk.CTkLabel(perf_frame, text="Диск: -- MB/s")
        self.disk_label.pack(anchor="w", padx=10, pady=(2, 10))

    def create_control_panel(self, parent):
        """Панель управления записью"""
        control_frame = ctk.CTkFrame(parent)
        control_frame.pack(fill="x", pady=(10, 0))

        # Статус записи
        self.status_label = ctk.CTkLabel(control_frame, text="Готов к записи",
                                        font=ctk.CTkFont(size=16, weight="bold"))
        self.status_label.pack(pady=10)

        # Кнопки управления
        button_frame = ctk.CTkFrame(control_frame)
        button_frame.pack(pady=10)

        self.record_btn = ctk.CTkButton(button_frame, text="Начать запись",
                                       command=self.toggle_recording, width=150, height=40)
        self.record_btn.pack(side="left", padx=5)

        self.pause_btn = ctk.CTkButton(button_frame, text="Пауза",
                                      command=self.toggle_pause, width=100, height=40, state="disabled")
        self.pause_btn.pack(side="left", padx=5)

        self.screenshot_btn = ctk.CTkButton(button_frame, text="Скриншот",
                                           command=self.take_screenshot, width=100, height=40)
        self.screenshot_btn.pack(side="left", padx=5)

        # Время записи
        self.time_label = ctk.CTkLabel(control_frame, text="00:00:00")
        self.time_label.pack(pady=5)

    def on_profile_change(self, value):
        """Обработка изменения профиля качества"""
        self.settings['quality_profile'] = value
        self.update_profile_info()
        self.save_settings()

    def update_profile_info(self):
        """Обновление информации о профиле"""
        profile = self.quality_profiles[self.profile_var.get()]
        info_text = f"""Название: {profile['name']}
Разрешение: {profile['resolution']}
FPS: {profile['fps']}
Битрейт: {profile['bitrate']} kbps
Кодек: {profile['codec']}
Пресет: {profile['preset']}
CRF: {profile['crf']}"""

        self.profile_info.delete("1.0", "end")
        self.profile_info.insert("1.0", info_text)

    def browse_output_folder(self):
        """Выбор папки для сохранения записей"""
        folder = filedialog.askdirectory(title="Выберите папку для записей")
        if folder:
            self.output_path_var.set(folder)
            self.settings['output_dir'] = folder
            self.save_settings()

    def setup_hotkeys(self):
        """Настройка горячих клавиш"""
        try:
            keyboard.add_hotkey('f9', self.toggle_recording)
            keyboard.add_hotkey('f10', self.toggle_pause)
            keyboard.add_hotkey('f11', self.take_screenshot)
        except Exception as e:
            print(f"Ошибка настройки горячих клавиш: {e}")

    def toggle_recording(self):
        """Переключение записи"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()

    def start_recording(self):
        """Начало записи"""
        try:
            # Проверяем FFmpeg
            if not self.ffmpeg_path_var.get().strip():
                messagebox.showerror("Ошибка", "Укажите путь к FFmpeg")
                return

            # Создаем папку если не существует
            output_dir = self.output_path_var.get()
            os.makedirs(output_dir, exist_ok=True)

            # Генерируем команду FFmpeg
            cmd, output_file = self.get_ffmpeg_command()

            # Запускаем FFmpeg
            self.ffmpeg_process = subprocess.Popen(cmd,
                                                  stdin=subprocess.PIPE,
                                                  stdout=subprocess.PIPE,
                                                  stderr=subprocess.PIPE)

            self.is_recording = True
            self.start_time = time.time()
            self.output_file = output_file

            # Обновляем интерфейс
            self.record_btn.configure(text="Остановить запись")
            self.pause_btn.configure(state="normal")
            self.status_label.configure(text="Идет запись...")

            messagebox.showinfo("Запись началась", f"Запись сохраняется в:\n{output_file}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось начать запись:\n{str(e)}")

    def stop_recording(self):
        """Остановка записи"""
        if self.ffmpeg_process:
            self.ffmpeg_process.stdin.write(b'q')
            self.ffmpeg_process.stdin.flush()
            self.ffmpeg_process.wait()
            self.ffmpeg_process = None

        self.is_recording = False
        self.is_paused = False

        # Обновляем интерфейс
        self.record_btn.configure(text="Начать запись")
        self.pause_btn.configure(state="disabled", text="Пауза")
        self.status_label.configure(text="Запись остановлена")

        if self.output_file:
            messagebox.showinfo("Запись завершена", f"Файл сохранен:\n{self.output_file}")

    def toggle_pause(self):
        """Переключение паузы (заглушка - FFmpeg не поддерживает паузу напрямую)"""
        if not self.is_recording:
            return

        self.is_paused = not self.is_paused
        if self.is_paused:
            self.pause_btn.configure(text="Продолжить")
            self.status_label.configure(text="Запись на паузе")
        else:
            self.pause_btn.configure(text="Пауза")
            self.status_label.configure(text="Идет запись...")

    def take_screenshot(self):
        """Создание скриншота"""
        try:
            with mss.mss() as sct:
                screenshot = sct.grab(sct.monitors[1])  # Основной монитор

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = os.path.join(
                    self.output_path_var.get(),
                    f"minecraft_screenshot_{timestamp}.png"
                )

                # Сохраняем скриншот
                mss.tools.to_png(screenshot.rgb, screenshot.size, output=filename)
                messagebox.showinfo("Скриншот", f"Скриншот сохранен:\n{filename}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось создать скриншот:\n{str(e)}")
