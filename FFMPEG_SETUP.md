# Установка и настройка FFmpeg

## Что такое FFmpeg?

FFmpeg - это мощная библиотека для работы с видео и аудио. Наша программа использует FFmpeg для записи экрана с максимальным качеством.

## Установка FFmpeg на Windows

### Способ 1: Скачивание готовой сборки (Рекомендуется)

1. **Перейдите на официальный сайт:**
   - Откройте https://ffmpeg.org/download.html
   - Нажмите на "Windows" в разделе "Get packages & executable files"

2. **Скачайте FFmpeg:**
   - Выберите "Windows builds by Btb<PERSON>" (рекомендуется)
   - Скачайте "ffmpeg-master-latest-win64-gpl.zip"
   - Размер файла: ~100 MB

3. **Распакуйте архив:**
   - Создайте папку `C:\ffmpeg`
   - Распакуйте содержимое архива в эту папку
   - Структура должна быть: `C:\ffmpeg\bin\ffmpeg.exe`

4. **Добавьте в PATH (опционально):**
   - Откройте "Параметры системы" → "Дополнительные параметры системы"
   - Нажмите "Переменные среды"
   - В "Системные переменные" найдите "Path" и нажмите "Изменить"
   - Добавьте путь: `C:\ffmpeg\bin`
   - Нажмите "ОК" во всех окнах

### Способ 2: Через пакетный менеджер

**Chocolatey:**
```powershell
# Установите Chocolatey (если не установлен)
# Затем выполните:
choco install ffmpeg
```

**Winget:**
```powershell
winget install ffmpeg
```

## Проверка установки

1. **Через командную строку:**
   ```cmd
   ffmpeg -version
   ```
   Должна появиться информация о версии FFmpeg.

2. **Через нашу программу:**
   - Запустите программу
   - Укажите путь к `ffmpeg.exe` (например: `C:\ffmpeg\bin\ffmpeg.exe`)
   - Нажмите кнопку "Проверить FFmpeg"

## Рекомендуемые настройки для максимального качества

### Для записи 4K 120fps:
- **Кодек:** H.265 (HEVC) с аппаратным ускорением
- **Битрейт:** 100-150 Mbps
- **Пресет:** medium или fast
- **CRF:** 18-20 (чем меньше, тем лучше качество)

### Системные требования:
- **CPU:** Intel i7-10700K или AMD Ryzen 7 3700X (минимум)
- **GPU:** NVIDIA RTX 3070 или AMD RX 6700 XT (для аппаратного ускорения)
- **RAM:** 32 GB (рекомендуется)
- **Диск:** SSD NVMe с высокой скоростью записи

## Оптимизация производительности

### Настройки Windows:
1. **Включите игровой режим:**
   - Настройки → Игры → Игровой режим → Включить

2. **Настройте план питания:**
   - Панель управления → Электропитание → Высокая производительность

3. **Отключите ненужные программы:**
   - Закройте браузер, мессенджеры и другие программы
   - Отключите антивирус на время записи (осторожно!)

### Настройки записи:
1. **Используйте отдельный диск для записи**
2. **Записывайте на SSD, а не на HDD**
3. **Освободите место на диске (минимум 50 GB свободного места)**

## Устранение проблем

### FFmpeg не найден:
- Проверьте правильность пути к файлу
- Убедитесь, что файл `ffmpeg.exe` существует
- Попробуйте переустановить FFmpeg

### Низкая производительность:
- Уменьшите разрешение записи
- Снизьте битрейт
- Используйте более быстрый пресет (ultrafast, superfast, veryfast)
- Включите аппаратное ускорение GPU

### Большие файлы:
- Используйте кодек H.265 вместо H.264
- Увеличьте значение CRF (снизит качество, но уменьшит размер)
- Используйте двухпроходное кодирование

### Ошибки кодирования:
- Обновите драйверы видеокарты
- Попробуйте отключить аппаратное ускорение
- Проверьте, поддерживает ли ваша видеокарта выбранный кодек

## Полезные ссылки

- **Официальный сайт FFmpeg:** https://ffmpeg.org/
- **Документация:** https://ffmpeg.org/documentation.html
- **Сборки для Windows:** https://www.gyan.dev/ffmpeg/builds/
- **Альтернативные сборки:** https://github.com/BtbN/FFmpeg-Builds/releases

## Поддержка

Если у вас возникли проблемы с установкой или настройкой FFmpeg:

1. Проверьте этот файл еще раз
2. Убедитесь, что ваша система соответствует требованиям
3. Попробуйте другую сборку FFmpeg
4. Обратитесь за помощью в Issues репозитория

**Важно:** Для записи 4K 120fps требуется мощная система. Если ваш компьютер не справляется, попробуйте профили с меньшим разрешением или FPS.
