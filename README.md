# Minecraft 4K 120fps Recorder

Профессиональная программа для записи Minecraft с максимальным качеством 4K при 120 FPS.

## Особенности

- **4K 120fps запись** - Максимальное качество и плавность
- **Оптимизированные кодеки** - H.265/HEVC для лучшего сжатия
- **Умный захват экрана** - Автоматическое определение окна Minecraft
- **Профили качества** - Предустановки для разных сценариев
- **Мониторинг производительности** - Отслеживание FPS и ресурсов в реальном времени
- **Горячие клавиши** - Управление без переключения окон
- **Минимальная задержка** - Оптимизация для игрового процесса

## Системные требования

### Минимальные:
- Windows 10/11 64-bit
- Intel i5-8400 / AMD Ryzen 5 2600
- 16 GB RAM
- NVIDIA GTX 1660 / AMD RX 580
- 50 GB свободного места

### Рекомендуемые для 4K 120fps:
- Windows 11 64-bit
- Intel i7-12700K / AMD Ryzen 7 5800X
- 32 GB RAM
- NVIDIA RTX 4070 / AMD RX 7800 XT
- SSD NVMe с высокой скоростью записи
- 200+ GB свободного места

## Установка

1. Установите Python 3.9+
2. Установите FFmpeg:
   ```bash
   # Скачайте FFmpeg с https://ffmpeg.org/download.html
   # Добавьте в PATH системы
   ```
3. Установите зависимости:
   ```bash
   pip install -r requirements.txt
   ```
4. Запустите программу:
   ```bash
   python main.py
   ```

## Использование

### Быстрый старт:
1. Запустите программу
2. Выберите профиль качества
3. Настройте область захвата
4. Нажмите "Начать запись" или F9

### Горячие клавиши:
- **F9** - Начать/остановить запись
- **F10** - Пауза/продолжить
- **F11** - Сделать скриншот
- **F12** - Открыть настройки

### Профили качества:

#### Максимальное качество (4K 120fps)
- Разрешение: 3840x2160
- FPS: 120
- Битрейт: 100-150 Mbps
- Кодек: H.265 (HEVC)

#### Стриминг (1440p 60fps)
- Разрешение: 2560x1440
- FPS: 60
- Битрейт: 15-25 Mbps
- Кодек: H.264

#### Архивная (1080p 30fps)
- Разрешение: 1920x1080
- FPS: 30
- Битрейт: 8-12 Mbps
- Кодек: H.264

## Оптимизация производительности

### Настройки Windows:
1. Включите "Игровой режим"
2. Отключите Windows Defender в реальном времени (временно)
3. Закройте ненужные программы
4. Используйте "Высокая производительность" в настройках питания

### Настройки Minecraft:
1. Используйте Optifine или Sodium
2. Ограничьте FPS до 120
3. Отключите V-Sync в игре
4. Используйте полноэкранный режим

## Устранение неполадок

### Низкий FPS записи:
- Уменьшите разрешение записи
- Снизьте битрейт
- Используйте аппаратное ускорение GPU
- Закройте другие программы

### Большой размер файлов:
- Используйте H.265 кодек
- Снизьте битрейт
- Включите двухпроходное кодирование

### Задержки в игре:
- Используйте Game Mode
- Снизьте приоритет процесса записи
- Записывайте на отдельный диск

## Лицензия

MIT License - свободное использование и модификация.

## Поддержка

Для вопросов и предложений создавайте Issues в репозитории.
